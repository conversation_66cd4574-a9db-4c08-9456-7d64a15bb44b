Pod::Spec.new do |spec|
  spec.name          = 'MelpCodec'
  spec.version       = '1.0.0'
  spec.summary       = 'MELP Audio Codec for iOS'
  spec.description   = <<-DESC
    Mixed Excitation Linear Prediction (MELP) audio codec implementation for iOS.
    Supports 1.2kbps and 2.4kbps encoding/decoding with 8kHz sampling rate.
  DESC
  
  spec.homepage      = 'https://github.com/example/aitalk'
  spec.license       = { :type => 'MIT', :text => 'MIT License' }
  spec.author        = { 'aiTalk Team' => '<EMAIL>' }
  spec.source        = { :path => '.' }
  
  spec.ios.deployment_target = '15.0'
  
  # 包含共享源码
  spec.source_files = [
    '../../shared/melp/MELPe_fxp/*.{c,h}',
    '../../shared/melp/wrapper/*.{c,h}'
  ]
  
  # 排除包含main()函数的演示程序
  spec.exclude_files = [
    '../../shared/melp/MELPe_fxp/melp_enc.c',
    '../../shared/melp/MELPe_fxp/melp_dec.c',
    '../../shared/melp/MELPe_fxp/sc12enc.c',
    '../../shared/melp/MELPe_fxp/sc24enc.c',
    '../../shared/melp/MELPe_fxp/sc12dec.c',
    '../../shared/melp/MELPe_fxp/sc24dec.c',
    '../../shared/melp/MELPe_fxp/sc1200.c'
  ]
  
  # 公开头文件
  spec.public_header_files = [
    '../../shared/melp/wrapper/melp_wrapper.h'
  ]
  
  # 编译设置
  spec.compiler_flags = '-DIOS_BUILD'
  spec.libraries = 'm'
  
  # 禁用ARC，因为这是纯C代码
  spec.requires_arc = false
  
  # 支持的架构
  spec.pod_target_xcconfig = {
    'VALID_ARCHS' => 'arm64 x86_64',
    'HEADER_SEARCH_PATHS' => '$(PODS_TARGET_SRCROOT)/../../shared/melp/MELPe_fxp $(PODS_TARGET_SRCROOT)/../../shared/melp/wrapper'
  }
end
